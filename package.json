{"name": "swiss-budget-pro", "version": "1.0.0", "description": "Advanced Swiss financial planning and retirement calculator", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@tailwindcss/postcss": "^4.1.7", "autoprefixer": "^10.4.21", "d3": "^7.9.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "postcss": "^8.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "tailwindcss": "^4.1.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/d3": "^7.4.3", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-i18next": "^7.8.3", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.0.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "keywords": ["react", "typescript", "financial-planning", "retirement-calculator", "swiss-finance", "budgeting"], "author": "Swiss Budget Pro Team", "license": "MIT"}