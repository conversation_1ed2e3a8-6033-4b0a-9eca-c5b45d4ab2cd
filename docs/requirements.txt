# Sphinx Documentation Requirements
# Install with: pip install -r requirements.txt

# Core Sphinx
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
furo>=2023.9.10

# Extensions
myst-parser>=2.0.0
sphinx-copybutton>=0.5.2
sphinx-design>=0.5.0
sphinx-tabs>=3.4.1
sphinx-autobuild>=2021.3.14

# Additional utilities
sphinx-external-toc>=0.3.1
sphinx-togglebutton>=0.3.2
sphinx-inline-tabs>=2023.4.21

# Development tools
doc8>=1.1.1
rstcheck>=6.2.0
linkchecker>=10.2.1

# Optional enhancements
sphinx-notfound-page>=0.8.3
sphinx-sitemap>=2.5.1
sphinxext-opengraph>=0.8.2
