# API Reference

Complete API documentation for Swiss Budget Pro's calculation engine, components, and utilities. This reference provides detailed information for developers integrating with or extending the application.

## Overview

Swiss Budget Pro exposes a comprehensive API for financial calculations, data management, and UI components. The API is designed to be type-safe, well-documented, and easy to use.

```{admonition} 🔧 API Design Principles
:class: tip

**Type Safety**: All APIs are fully typed with TypeScript
**Immutability**: Functions don't mutate input data
**Predictability**: Same inputs always produce same outputs
**Performance**: Optimized for real-time calculations
**Extensibility**: Easy to extend and customize
```

## Core Calculation Engine

### Financial Calculations

#### `calculateNetWorth(assets, liabilities)`

Calculates total net worth from assets and liabilities.

```typescript
function calculateNetWorth(
  assets: Asset[],
  liabilities: Liability[]
): number

interface Asset {
  id: string;
  name: string;
  value: number;
  type: AssetType;
  currency: Currency;
}

interface Liability {
  id: string;
  name: string;
  balance: number;
  interestRate: number;
  currency: Currency;
}

// Example usage
const assets = [
  { id: '1', name: 'Savings', value: 50000, type: 'cash', currency: 'CHF' },
  { id: '2', name: 'Stocks', value: 100000, type: 'investment', currency: 'CHF' }
];

const liabilities = [
  { id: '1', name: 'Mortgage', balance: 300000, interestRate: 0.025, currency: 'CHF' }
];

const netWorth = calculateNetWorth(assets, liabilities);
// Returns: -150000
```

#### `calculateSavingsRate(income, expenses)`

Calculates monthly savings rate as a percentage.

```typescript
function calculateSavingsRate(
  income: number,
  expenses: number
): number

// Example usage
const savingsRate = calculateSavingsRate(10000, 7000);
// Returns: 0.3 (30%)
```

#### `calculateFIREProgress(currentWealth, fireGoal)`

Calculates progress toward FIRE goal as a percentage.

```typescript
function calculateFIREProgress(
  currentWealth: number,
  fireGoal: number
): number

// Example usage
const progress = calculateFIREProgress(500000, 1500000);
// Returns: 0.333 (33.3%)
```

### Swiss Tax Calculations

#### `calculateSwissTax(income, canton, municipality)`

Comprehensive Swiss tax calculation for all three levels.

```typescript
function calculateSwissTax(
  income: number,
  canton: Canton,
  municipality?: Municipality
): TaxResult

interface TaxResult {
  federal: {
    amount: number;
    rate: number;
  };
  cantonal: {
    amount: number;
    rate: number;
    multiplier: number;
  };
  municipal: {
    amount: number;
    rate: number;
    multiplier: number;
  };
  total: {
    amount: number;
    effectiveRate: number;
  };
  deductions: TaxDeduction[];
}

// Example usage
const taxResult = calculateSwissTax(120000, 'ZH', 'Zurich');
console.log(taxResult.total.amount); // Total tax owed
console.log(taxResult.total.effectiveRate); // Effective tax rate
```

#### `calculatePillar3aBenefit(contribution, marginalTaxRate)`

Calculates tax savings from Pillar 3a contributions.

```typescript
function calculatePillar3aBenefit(
  contribution: number,
  marginalTaxRate: number
): Pillar3aBenefit

interface Pillar3aBenefit {
  annualSavings: number;
  lifetimeSavings: number;
  withdrawalTax: number;
  netBenefit: number;
}

// Example usage
const benefit = calculatePillar3aBenefit(7056, 0.35);
// Returns tax savings and withdrawal implications
```

### Projection Calculations

#### `projectWealth(params)`

Projects wealth accumulation over time with various scenarios.

```typescript
function projectWealth(params: ProjectionParams): WealthProjection[]

interface ProjectionParams {
  initialWealth: number;
  monthlyContribution: number;
  annualReturn: number;
  inflationRate: number;
  timeHorizon: number; // years
  scenarios?: ScenarioParams[];
}

interface WealthProjection {
  year: number;
  nominalWealth: number;
  realWealth: number;
  contributions: number;
  returns: number;
  fireProgress: number;
}

// Example usage
const projections = projectWealth({
  initialWealth: 100000,
  monthlyContribution: 3000,
  annualReturn: 0.07,
  inflationRate: 0.02,
  timeHorizon: 25
});
```

#### `calculateFIRETimeline(params)`

Calculates when FIRE goal will be achieved.

```typescript
function calculateFIRETimeline(params: FIREParams): FIRETimeline

interface FIREParams {
  currentWealth: number;
  monthlyContribution: number;
  fireGoal: number;
  expectedReturn: number;
  withdrawalRate: number;
  continuingIncome?: ContinuingIncome; // Income that continues after retirement
}

interface ContinuingIncome {
  companyIncome: number;        // Business/freelance income
  rentalIncome: number;         // Property rental income
  investmentIncome: number;     // Dividends, interest, royalties
  growthRate: number;           // Annual growth rate for continuing income
}

interface FIRETimeline {
  yearsToFIRE: number;
  fireAge: number;
  finalWealth: number;
  monthlyIncome: number;
  continuingMonthlyIncome: number; // Income that continues after retirement
  confidence: number;
}

// Example usage without continuing income
const basicTimeline = calculateFIRETimeline({
  currentWealth: 200000,
  monthlyContribution: 4000,
  fireGoal: 1500000,
  expectedReturn: 0.07,
  withdrawalRate: 0.04
});

// Example usage with continuing income (reduces FIRE number needed)
const timelineWithIncome = calculateFIRETimeline({
  currentWealth: 200000,
  monthlyContribution: 4000,
  fireGoal: 1200000, // Lower FIRE goal due to continuing income
  expectedReturn: 0.07,
  withdrawalRate: 0.04,
  continuingIncome: {
    companyIncome: 2000,      // CHF 2,000/month from business
    rentalIncome: 1500,       // CHF 1,500/month from rental
    investmentIncome: 500,    // CHF 500/month from dividends
    growthRate: 0.03          // 3% annual growth
  }
});
```

## Data Management API

### Local Storage Interface

#### `useLocalStorage(key, defaultValue)`

Custom hook for persistent local storage with type safety.

```typescript
function useLocalStorage<T>(
  key: string,
  defaultValue: T
): [T, (value: T) => void]

// Example usage
const [userProfile, setUserProfile] = useLocalStorage<UserProfile>(
  'userProfile',
  defaultUserProfile
);
```

#### `exportData(format, scope)`

Export user data in various formats.

```typescript
function exportData(
  format: ExportFormat,
  scope: ExportScope
): Promise<Blob>

type ExportFormat = 'json' | 'csv' | 'excel' | 'pdf';
type ExportScope = 'all' | 'scenarios' | 'historical' | 'settings';

// Example usage
const dataBlob = await exportData('json', 'all');
const url = URL.createObjectURL(dataBlob);
// Trigger download
```

#### `importData(file, format)`

Import data from external sources.

```typescript
function importData(
  file: File,
  format: ImportFormat
): Promise<ImportResult>

interface ImportResult {
  success: boolean;
  data?: any;
  errors?: ImportError[];
  warnings?: ImportWarning[];
}

// Example usage
const result = await importData(file, 'csv');
if (result.success) {
  // Process imported data
}
```

## Component API

### Chart Components

#### `<WealthProjectionChart />`

Interactive wealth projection visualization.

```typescript
interface WealthProjectionChartProps {
  data: WealthProjection[];
  width?: number;
  height?: number;
  showScenarios?: boolean;
  interactive?: boolean;
  theme?: ChartTheme;
  onDataPointClick?: (point: WealthProjection) => void;
}

// Example usage
<WealthProjectionChart
  data={projections}
  width={800}
  height={400}
  showScenarios={true}
  onDataPointClick={(point) => console.log(point)}
/>
```

#### `<SavingsRateChart />`

Savings rate trend visualization.

```typescript
interface SavingsRateChartProps {
  data: SavingsRateData[];
  target?: number;
  showTrend?: boolean;
  timeRange?: TimeRange;
}

// Example usage
<SavingsRateChart
  data={savingsData}
  target={0.25}
  showTrend={true}
  timeRange="1year"
/>
```

### Form Components

#### `<FinancialInputForm />`

Comprehensive financial data input form.

```typescript
interface FinancialInputFormProps {
  initialData?: FinancialData;
  onSubmit: (data: FinancialData) => void;
  onValidationError?: (errors: ValidationError[]) => void;
  readOnly?: boolean;
  sections?: FormSection[];
}

// Example usage
<FinancialInputForm
  initialData={currentData}
  onSubmit={handleDataUpdate}
  sections={['income', 'expenses', 'assets']}
/>
```

## Utility Functions

### Number Formatting

#### `formatCurrency(amount, currency, locale)`

Format numbers as currency with proper Swiss conventions.

```typescript
function formatCurrency(
  amount: number,
  currency: Currency = 'CHF',
  locale: Locale = 'de-CH'
): string

// Example usage
formatCurrency(1234.56, 'CHF', 'de-CH');
// Returns: "CHF 1'234.56"

formatCurrency(1234.56, 'CHF', 'fr-CH');
// Returns: "CHF 1'234,56"
```

#### `formatPercentage(value, decimals)`

Format decimal values as percentages.

```typescript
function formatPercentage(
  value: number,
  decimals: number = 1
): string

// Example usage
formatPercentage(0.2567, 1);
// Returns: "25.7%"
```

### Date Utilities

#### `calculateAge(birthDate, referenceDate)`

Calculate age with precision for financial planning.

```typescript
function calculateAge(
  birthDate: Date,
  referenceDate: Date = new Date()
): number

// Example usage
const age = calculateAge(new Date('1985-06-15'));
// Returns current age as decimal
```

#### `addYears(date, years)`

Add years to a date with leap year handling.

```typescript
function addYears(date: Date, years: number): Date

// Example usage
const futureDate = addYears(new Date(), 25);
// Returns date 25 years from now
```

### Validation Functions

#### `validateFinancialData(data)`

Comprehensive validation of financial input data.

```typescript
function validateFinancialData(
  data: FinancialData
): ValidationResult

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

interface ValidationError {
  field: string;
  message: string;
  code: ErrorCode;
}

// Example usage
const validation = validateFinancialData(userInput);
if (!validation.isValid) {
  console.log(validation.errors);
}
```

## Swiss-Specific APIs

### Canton Information

#### `getCantonInfo(cantonCode)`

Retrieve detailed information about Swiss cantons.

```typescript
function getCantonInfo(cantonCode: CantonCode): CantonInfo

interface CantonInfo {
  code: CantonCode;
  name: string;
  nameDE: string;
  nameFR: string;
  nameIT: string;
  capital: string;
  taxMultiplier: number;
  wealthTaxRate: number;
  costOfLivingIndex: number;
  qualityOfLifeScore: number;
}

// Example usage
const zurichInfo = getCantonInfo('ZH');
console.log(zurichInfo.taxMultiplier); // 1.0
```

#### `compareCantonsForFIRE(profile, cantons)`

Compare multiple cantons for FIRE optimization.

```typescript
function compareCantonsForFIRE(
  profile: UserProfile,
  cantons: CantonCode[]
): CantonComparison[]

interface CantonComparison {
  canton: CantonInfo;
  annualTaxSavings: number;
  costOfLivingDifference: number;
  netBenefit: number;
  fireTimelineImpact: number;
  qualityOfLifeScore: number;
  recommendation: RecommendationLevel;
}

// Example usage
const comparison = compareCantonsForFIRE(userProfile, ['ZH', 'ZG', 'SZ']);
```

## Error Handling

### Error Types

```typescript
enum ErrorCode {
  INVALID_INPUT = 'INVALID_INPUT',
  CALCULATION_ERROR = 'CALCULATION_ERROR',
  DATA_NOT_FOUND = 'DATA_NOT_FOUND',
  STORAGE_ERROR = 'STORAGE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

class SwissBudgetProError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'SwissBudgetProError';
  }
}
```

### Error Handling Patterns

```typescript
// Function with error handling
function safeCalculation<T>(
  calculation: () => T,
  fallback: T
): T {
  try {
    return calculation();
  } catch (error) {
    console.error('Calculation error:', error);
    return fallback;
  }
}

// React error boundary
class CalculationErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Calculation component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

## Performance Considerations

### Memoization

```typescript
// Expensive calculations are memoized
const memoizedProjection = useMemo(() => {
  return projectWealth(projectionParams);
}, [projectionParams]);

// Debounced updates for real-time calculations
const debouncedCalculation = useDebouncedCallback(
  (data: FinancialData) => {
    updateCalculations(data);
  },
  300
);
```

### Optimization Tips

1. **Batch Updates**: Group multiple state updates
2. **Lazy Loading**: Load components on demand
3. **Virtual Scrolling**: For large data sets
4. **Web Workers**: For heavy calculations (planned)
5. **Caching**: Cache expensive calculation results

---

*This API reference is automatically generated from TypeScript definitions and is kept up-to-date with the latest codebase changes.*
