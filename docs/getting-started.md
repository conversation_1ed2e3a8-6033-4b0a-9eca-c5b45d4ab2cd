# Getting Started with Swiss Budget Pro

Welcome to Swiss Budget Pro! This guide will help you get up and running with the ultimate Swiss FIRE planning tool in just a few minutes.

## What You'll Learn

By the end of this guide, you'll know how to:

- Set up your basic financial profile
- Input your income and expenses
- Configure Swiss-specific settings
- Save and manage your financial scenarios
- Interpret your FIRE projections

## Prerequisites

Swiss Budget Pro runs entirely in your web browser - no installation required! You'll need:

- A modern web browser (Chrome, Firefox, Safari, or Edge)
- Basic knowledge of your financial situation
- Your Swiss tax information (canton, income, etc.)

```{admonition} 💡 Pro Tip
:class: tip

Have your latest tax return and salary statements handy. This will help you input accurate data for the most reliable projections.
```

## Step 1: Access Swiss Budget Pro

1. Open your web browser
2. Navigate to the Swiss Budget Pro application
3. The application loads automatically - no login required!

```{admonition} 🔒 Privacy First
:class: note

Swiss Budget Pro stores all your data locally in your browser. Your financial information never leaves your device, ensuring complete privacy and security.
```

## Step 2: Basic Profile Setup

### Personal Information

Start by setting up your basic profile in the **Overview** tab:

```{code-block} text
Current Age: [Your current age]
Retirement Age: [When you want to retire]
Canton: [Your Swiss canton for tax calculations]
```

**Example:**
- Current Age: 32
- Retirement Age: 55
- Canton: Zurich

### Financial Starting Point

Input your current financial position:

```{code-block} text
Current Savings: CHF [Your total savings]
Current Pension (BVG): CHF [Your pension fund balance]
Current Pillar 3a: CHF [Your 3a savings]
```

**Example:**
- Current Savings: CHF 150,000
- Current Pension: CHF 85,000
- Current Pillar 3a: CHF 45,000

## Step 3: Income Configuration

Navigate to the **Budget Plan** tab to set up your income streams.

### Primary Income

```{code-block} text
Monthly Gross Income: CHF [Your monthly salary]
Income Percentage: [Percentage you expect to maintain]
```

**Example:**
- Monthly Gross Income: CHF 8,500
- Income Percentage: 100% (full-time work)

### Additional Income Streams

If you have additional income sources:

1. **Company Income**: Future business or freelance income
2. **HSLU Income**: Academic or consulting income
3. **RUAG Income**: Specific employer income

```{admonition} 📈 Growth Planning
:class: tip

Use the **Company Growth** tab to model income growth from side businesses or career advancement. This is powerful for FIRE planning!
```

## Step 4: Expense Tracking

### Essential Expenses

Add your monthly expenses by category:

```{tabs}

```{tab} Housing
- Rent/Mortgage: CHF 2,200
- Utilities: CHF 150
- Insurance: CHF 300
```

```{tab} Living
- Food: CHF 800
- Transportation: CHF 400
- Healthcare: CHF 350
```

```{tab} Lifestyle
- Entertainment: CHF 300
- Hobbies: CHF 200
- Travel: CHF 500
```

### Expense Categories

For each expense, specify:
- **Category**: Descriptive name
- **Amount**: Monthly amount in CHF
- **Essential**: Whether it's a necessity or discretionary

```{code-block} javascript
// Example expense entry
{
  category: "Rent",
  amount: 2200,
  essential: true
}
```

## Step 5: Swiss-Specific Configuration

### Tax Settings

Swiss Budget Pro automatically calculates taxes based on your canton, but you can fine-tune:

1. **Canton Selection**: Choose your tax canton
2. **Tax Optimization**: Enable Pillar 3a optimization
3. **Wealth Tax**: Consider wealth tax implications

### Pillar 3a Strategy

Configure your Pillar 3a contributions:

```{code-block} text
Annual 3a Contribution: CHF [Up to legal maximum]
3a Growth Rate: [Expected annual return]
3a Withdrawal Strategy: [Staggered vs lump sum]
```

**2024 Limits:**
- Employed with pension fund: CHF 7,056
- Self-employed without pension fund: CHF 35,280

## Step 6: Investment Assumptions

Set realistic return expectations:

### Conservative Approach
```{code-block} text
Expected Return: 4-5%
Inflation Rate: 1.5-2%
```

### Moderate Approach
```{code-block} text
Expected Return: 6-7%
Inflation Rate: 2%
```

### Aggressive Approach
```{code-block} text
Expected Return: 8-9%
Inflation Rate: 2.5%
```

```{admonition} ⚠️ Reality Check
:class: warning

Be conservative with return assumptions. It's better to be pleasantly surprised than disappointed. Historical Swiss market returns average 6-8% annually.
```

## Step 7: Save Your Scenario

### Auto-Save Feature

Swiss Budget Pro automatically saves your data every 30 seconds, but you can also:

1. **Manual Save**: Click the save button for immediate backup
2. **Named Scenarios**: Save different planning scenarios
3. **Export Data**: Download your data for external analysis

### Creating Multiple Scenarios

Use the **Data & History** tab to:

```{code-block} text
1. Save current plan as "Conservative Plan"
2. Modify assumptions for "Aggressive Plan"
3. Compare scenarios side-by-side
```

## Step 8: Interpret Your Results

### Key Metrics to Watch

```{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 💰 Savings Rate
Your monthly savings as a percentage of income. Aim for 20%+ for FIRE.
```

```{grid-item-card} 🎯 FIRE Progress
Percentage toward your financial independence goal.
```

```{grid-item-card} 📅 FIRE Date
Projected date when you can retire based on current trajectory.
```

```{grid-item-card} 💎 Net Worth
Total assets minus liabilities, growing toward your FIRE number.
```

### Understanding the Charts

1. **Projection Chart**: Shows your wealth accumulation over time
2. **Historical Trends**: Tracks your actual progress (after using the app for months)
3. **Budget Visualization**: Breaks down income vs expenses

## Step 9: Optimization Tips

### Quick Wins

```{admonition} 🚀 Immediate Actions
:class: tip

1. **Maximize Pillar 3a**: Use the full annual limit for tax benefits
2. **Reduce Expenses**: Identify and eliminate unnecessary spending
3. **Increase Income**: Plan career moves or side income
4. **Tax Optimization**: Consider moving to a lower-tax canton
```

### Advanced Strategies

1. **Geographic Arbitrage**: Live in lower-cost cantons
2. **Tax-Loss Harvesting**: Optimize investment timing
3. **Pension Optimization**: Coordinate BVG and 3a withdrawals
4. **Real Estate**: Consider property investment strategies

## Next Steps

Now that you're set up, explore these advanced features:

```{toctree}
:maxdepth: 1

user-guide/financial-planning
user-guide/swiss-features
user-guide/data-management
user-guide/tax-optimization
```

## Common Questions

### "My projections seem too optimistic/pessimistic"

- Double-check your return assumptions
- Verify your expense calculations
- Consider inflation impact
- Review your savings rate

### "How accurate are the tax calculations?"

Swiss Budget Pro uses official tax rates but:
- Tax laws change annually
- Deductions vary by situation
- Consult a tax professional for complex situations

### "Can I trust the FIRE projections?"

Projections are estimates based on your inputs:
- Use conservative assumptions
- Plan for market volatility
- Review and adjust regularly
- Consider multiple scenarios

## Getting Help

```{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 📖 User Guide
:link: user-guide/index
:link-type: doc

Comprehensive guides for all features
```

```{grid-item-card} 🇨🇭 Swiss Features
:link: user-guide/swiss-features
:link-type: doc

Swiss-specific functionality
```

```{grid-item-card} 💬 Community
:link: https://github.com/swiss-budget-pro/discussions

Ask questions and share tips
```

```{grid-item-card} 🐛 Report Issues
:link: https://github.com/swiss-budget-pro/issues

Found a problem? Let us know!
```

---

**Congratulations!** You're now ready to start your Swiss FIRE journey with Swiss Budget Pro. Remember, financial independence is a marathon, not a sprint. Regular review and adjustment of your plan will help you stay on track toward your goals.
