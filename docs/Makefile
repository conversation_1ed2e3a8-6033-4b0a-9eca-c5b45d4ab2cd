# Minimal makefile for Sphinx documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Custom targets for Swiss Budget Pro documentation

# Install dependencies
install:
	pip install -r requirements.txt

# Development server with auto-reload
serve:
	sphinx-autobuild "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O) \
		--host 0.0.0.0 \
		--port 8000 \
		--open-browser \
		--watch ../retire.tsx \
		--watch ../src/ \
		--ignore "$(BUILDDIR)" \
		--ignore "**/node_modules/**" \
		--ignore "**/.git/**"

# Clean build with dependency check
clean-build: clean install html

# Check documentation quality
check:
	doc8 --max-line-length 100 .
	rstcheck -r .
	$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS) $(O)

# Build for production
production: clean
	$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O) -W --keep-going

# Build PDF documentation
pdf:
	$(SPHINXBUILD) -b latex "$(SOURCEDIR)" "$(BUILDDIR)/latex" $(SPHINXOPTS) $(O)
	@echo "Running LaTeX files through pdflatex..."
	$(MAKE) -C "$(BUILDDIR)/latex" all-pdf
	@echo "pdflatex finished; the PDF files are in $(BUILDDIR)/latex."

# Build EPUB documentation
epub:
	$(SPHINXBUILD) -b epub "$(SOURCEDIR)" "$(BUILDDIR)/epub" $(SPHINXOPTS) $(O)
	@echo "Build finished. The epub file is in $(BUILDDIR)/epub."

# Spell check (requires aspell)
spellcheck:
	@echo "Running spell check..."
	@find . -name "*.md" -o -name "*.rst" | xargs aspell --mode=sgml --personal=./.aspell.en.pws list | sort | uniq

# Generate API documentation (placeholder for future TypeScript integration)
api:
	@echo "API documentation generation not yet implemented"
	@echo "Future: TypeScript API docs will be generated here"

# Quick development cycle
dev: clean-build serve

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)
