# Swiss Budget Pro Documentation

This directory contains the complete Sphinx documentation for Swiss Budget Pro, the ultimate Swiss FIRE planning tool.

## 📚 Documentation Structure

```
docs/
├── conf.py                 # Sphinx configuration
├── index.md               # Main documentation homepage
├── getting-started.md     # Quick start guide
├── requirements.txt       # Python dependencies
├── Makefile              # Build automation
├── README.md             # This file
├── _static/              # Static assets (CSS, JS, images)
├── _templates/           # Custom Sphinx templates
├── user-guide/           # User documentation
├── developer-guide/      # Technical documentation
├── api-reference/        # API documentation
├── architecture/         # System architecture docs
├── advanced/             # Advanced topics
└── _build/               # Generated documentation (git-ignored)
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Installation

1. **Install dependencies:**
   ```bash
   cd docs
   pip install -r requirements.txt
   ```

2. **Build documentation:**
   ```bash
   make html
   ```

3. **Start development server:**
   ```bash
   make serve
   ```

4. **Open in browser:**
   Navigate to `http://localhost:8000`

## 🛠️ Development Workflow

### Building Documentation

```bash
# Clean build
make clean html

# Development server with auto-reload
make serve

# Production build with warnings as errors
make production

# Check documentation quality
make check
```

### Writing Documentation

#### File Format
- Use **Markdown** (`.md`) for content files
- MyST Parser enables advanced Markdown features
- Sphinx directives available through MyST syntax

#### Content Guidelines

**Structure:**
```markdown
# Page Title

Brief introduction paragraph.

## Section Heading

Content with proper formatting.

### Subsection

More detailed content.
```

**Swiss-Specific Content:**
- Use CHF for all currency examples
- Reference Swiss cantons and regulations
- Include Swiss tax considerations
- Mention Swiss-specific financial products

**Code Examples:**
```markdown
```{code-block} typescript
// TypeScript example
interface FinancialPlan {
  income: number;
  expenses: number;
  savingsRate: number;
}
```

**Admonitions:**
```markdown
```{admonition} 💡 Pro Tip
:class: tip

This is a helpful tip for users.
```

```{admonition} ⚠️ Important
:class: warning

This is important information.
```

#### Interactive Elements

**Grids:**
```markdown
```{grid} 1 2 2 3
:gutter: 3

```{grid-item-card} Title
:link: page-link
:link-type: doc

Description text
```
```

**Tabs:**
```markdown
```{tabs}

```{tab} Tab 1
Content for tab 1
```

```{tab} Tab 2
Content for tab 2
```
```

**Glossary:**
```markdown
```{glossary}

FIRE
    Financial Independence, Retire Early

Pillar 3a
    Swiss tax-advantaged retirement account
```

### Swiss Budget Pro Specific Features

#### Currency Formatting
```markdown
<!-- Automatic CHF formatting -->
<span class="currency">150000</span>

<!-- Manual formatting -->
CHF 150,000
```

#### Calculator Widgets
```markdown
<!-- Interactive FIRE calculator -->
<div class="fire-calculator"></div>

<!-- Savings rate calculator -->
<div class="savings-calculator"></div>
```

#### Swiss Flags and Symbols
```markdown
🇨🇭 Swiss-specific content
💰 Financial topics
📊 Data and analytics
🎯 Goals and targets
```

## 📖 Content Guidelines

### Writing Style

**Tone:**
- Professional but approachable
- Swiss-focused perspective
- Practical and actionable
- Encouraging for FIRE journey

**Technical Level:**
- Assume basic financial literacy
- Explain Swiss-specific concepts
- Provide examples and calculations
- Link to external resources when helpful

**Formatting:**
- Use bullet points for lists
- Include code examples for calculations
- Add visual elements (grids, cards, tabs)
- Maintain consistent heading hierarchy

### Swiss Context

**Always Consider:**
- 26 different cantonal tax systems
- Three-pillar retirement system
- Swiss franc (CHF) currency
- High cost of living
- Mandatory insurance requirements
- Cross-border considerations

**Common Swiss Terms:**
- AHV/AVS (state pension)
- BVG/LPP (occupational pension)
- Pillar 3a/3b (private savings)
- Canton (state)
- Gemeinde (municipality)
- Krankenkasse (health insurance)

## 🎨 Styling and Themes

### Custom CSS Classes

```css
.currency          /* CHF formatting */
.swiss-flag        /* Swiss flag emoji styling */
.financial-table   /* Financial data tables */
.formula          /* Mathematical formulas */
.btn-swiss        /* Swiss-themed buttons */
```

### Color Scheme

```css
--swiss-red: #dc143c      /* Swiss flag red */
--swiss-blue: #2563eb     /* Primary blue */
--swiss-green: #059669    /* Success/positive */
--swiss-gold: #d97706     /* Warning/attention */
--swiss-gray: #6b7280     /* Neutral text */
```

### Dark Mode Support

All custom styles include dark mode variants using:
```css
[data-theme="dark"] {
  /* Dark mode styles */
}
```

## 🔧 Advanced Configuration

### Sphinx Extensions

**Enabled Extensions:**
- `myst_parser` - Markdown support
- `sphinx_design` - Grid layouts and cards
- `sphinx_tabs` - Tabbed content
- `sphinx_copybutton` - Copy code blocks
- `sphinx.ext.intersphinx` - Cross-project links

**MyST Features:**
- Admonitions
- Code blocks with syntax highlighting
- Cross-references
- Math equations
- HTML integration

### Build Targets

```bash
make html          # HTML documentation
make pdf           # PDF output (requires LaTeX)
make epub          # EPUB e-book format
make linkcheck     # Check external links
make spellcheck    # Spell checking (requires aspell)
```

### Deployment

**GitHub Pages:**
```bash
# Build for production
make production

# Deploy to gh-pages branch
# (Configure GitHub Pages to serve from gh-pages branch)
```

**Custom Server:**
```bash
# Build static files
make html

# Copy _build/html/* to web server
rsync -av _build/html/ user@server:/var/www/docs/
```

## 🧪 Testing and Quality

### Documentation Quality Checks

```bash
# Check documentation syntax
make check

# Spell check
make spellcheck

# Link validation
sphinx-build -b linkcheck . _build/linkcheck
```

### Content Review Checklist

- [ ] All Swiss currency amounts use CHF
- [ ] Swiss-specific terms are explained
- [ ] Code examples are tested
- [ ] Links work correctly
- [ ] Images have alt text
- [ ] Mobile-friendly formatting
- [ ] Dark mode compatibility

## 📱 Mobile Optimization

The documentation is fully responsive and optimized for:
- Desktop browsers
- Tablet devices
- Mobile phones
- Print output

Key mobile features:
- Collapsible navigation
- Touch-friendly interface
- Readable font sizes
- Optimized images

## 🤝 Contributing

### Adding New Content

1. **Create new file** in appropriate directory
2. **Follow naming convention**: `kebab-case.md`
3. **Add to toctree** in parent index file
4. **Test locally** with `make serve`
5. **Submit pull request**

### Updating Existing Content

1. **Edit markdown files** directly
2. **Test changes** with development server
3. **Check for broken links**
4. **Verify mobile compatibility**
5. **Submit pull request**

### Reporting Issues

- **Content errors**: Create GitHub issue
- **Technical problems**: Check build logs
- **Feature requests**: Discuss in GitHub Discussions

## 📊 Analytics and Feedback

### User Feedback

- GitHub Discussions for community feedback
- GitHub Issues for bug reports
- Direct feedback through documentation comments

### Content Analytics

- Track popular pages
- Monitor search queries
- Identify content gaps
- Measure user engagement

## 🔮 Future Enhancements

### Planned Features

- [ ] Interactive financial calculators
- [ ] Video tutorials integration
- [ ] Multi-language support (German, French, Italian)
- [ ] API documentation auto-generation
- [ ] Advanced search functionality
- [ ] User contribution system

### Technical Improvements

- [ ] Automated testing pipeline
- [ ] Performance optimization
- [ ] SEO enhancements
- [ ] Accessibility improvements
- [ ] Progressive Web App features

---

## 📞 Support

**Documentation Issues:**
- GitHub Issues: Report bugs and request features
- GitHub Discussions: Community support and questions

**Swiss Budget Pro Support:**
- Main repository: Technical issues with the application
- Community forum: User discussions and tips

---

*Swiss Budget Pro Documentation - Built with ❤️ for the Swiss FIRE community*
