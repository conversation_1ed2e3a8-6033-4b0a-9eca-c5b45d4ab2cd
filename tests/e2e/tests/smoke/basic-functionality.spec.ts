import { expect, test } from '@playwright/test';

test.describe('Basic Functionality Smoke Tests', () => {
  test('App loads and basic functionality works', async ({ page }) => {
    console.log('🚀 Starting basic functionality test');
    
    // Navigate to the app
    await page.goto('http://localhost:5173');
    
    // Wait for the app to load
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Verify the app title
    const title = await page.locator('h1').first().textContent();
    expect(title).toContain('Swiss Budget Pro');
    
    console.log('✅ App loaded successfully');
    
    // Find and fill the monthly income input
    const incomeInput = page.locator('input[type="number"]').first();
    await incomeInput.fill('8000');
    
    console.log('✅ Income input filled');
    
    // Wait for calculations to complete
    await page.waitForTimeout(2000);
    
    // Verify some calculation results are visible
    const fireProjection = page.locator('text=CHF').first();
    await expect(fireProjection).toBeVisible();
    
    console.log('✅ Calculations completed');
    
    // Try clicking on different tabs
    const tabs = await page.locator('[role="tab"]').all();
    if (tabs.length > 1) {
      await tabs[1].click();
      await page.waitForTimeout(1000);
      console.log('✅ Tab navigation works');
    }
    
    console.log('🎉 Basic functionality test completed successfully');
  });

  test('Swiss tax calculation works', async ({ page }) => {
    console.log('🇨🇭 Testing Swiss tax calculation');
    
    await page.goto('http://localhost:5173');
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Fill in income
    const incomeInput = page.locator('input[type="number"]').first();
    await incomeInput.fill('10000');
    
    // Wait for calculations
    await page.waitForTimeout(3000);
    
    // Look for tax-related content
    const taxContent = page.locator('text=/tax|Tax|CHF/i').first();
    await expect(taxContent).toBeVisible();
    
    console.log('✅ Swiss tax calculation verified');
  });

  test('Language switching works', async ({ page }) => {
    console.log('🌍 Testing language switching');
    
    await page.goto('http://localhost:5173');
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Look for language switcher
    const languageSwitcher = page.locator('[data-testid="language-switcher"]');
    if (await languageSwitcher.count() > 0) {
      await languageSwitcher.click();
      
      // Look for German option
      const germanOption = page.locator('[data-testid="language-option-de"]');
      if (await germanOption.count() > 0) {
        await germanOption.click();
        await page.waitForTimeout(1000);
        console.log('✅ Language switching works');
      } else {
        console.log('ℹ️ German option not found, but switcher exists');
      }
    } else {
      console.log('ℹ️ Language switcher not found');
    }
  });

  test('Responsive design works', async ({ page }) => {
    console.log('📱 Testing responsive design');
    
    await page.goto('http://localhost:5173');
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.locator('h1')).toBeVisible();
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('h1')).toBeVisible();
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('h1')).toBeVisible();
    
    console.log('✅ Responsive design verified');
  });

  test('Data persistence works', async ({ page }) => {
    console.log('💾 Testing data persistence');
    
    await page.goto('http://localhost:5173');
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Fill in some data
    const incomeInput = page.locator('input[type="number"]').first();
    await incomeInput.fill('7500');
    
    // Wait for data to be saved
    await page.waitForTimeout(1000);
    
    // Reload the page
    await page.reload();
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Check if data persisted
    const persistedValue = await page.locator('input[type="number"]').first().inputValue();
    expect(persistedValue).toBe('7500');
    
    console.log('✅ Data persistence verified');
  });
});
