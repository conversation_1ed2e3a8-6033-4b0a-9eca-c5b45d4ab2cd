import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';

test.describe('Accessibility Compliance', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
  });

  test('WCAG 2.1 AA compliance check', async ({ page }) => {
    console.log('♿ Testing WCAG 2.1 AA compliance');
    
    // Check basic accessibility requirements
    await dashboardPage.checkAccessibility();
    
    // Verify page has proper structure
    const h1Elements = await page.locator('h1').count();
    expect(h1Elements).toBeGreaterThanOrEqual(1); // Should have at least one h1
    
    // Check for proper heading hierarchy
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    expect(headings.length).toBeGreaterThan(0);
    
    console.log('✅ Basic WCAG compliance verified');
  });

  test('Keyboard navigation', async ({ page }) => {
    console.log('⌨️ Testing keyboard navigation');
    
    // Test tab navigation through form fields
    await page.keyboard.press('Tab');
    await expect(dashboardPage.monthlyIncomeInput).toBeFocused();
    
    await page.keyboard.press('Tab');
    // Should move to next focusable element
    const focusedElement = await page.locator(':focus').first();
    expect(focusedElement).toBeTruthy();
    
    // Test tab navigation through main tabs
    await dashboardPage.overviewTab.focus();
    await page.keyboard.press('ArrowRight');
    await expect(dashboardPage.budgetTab).toBeFocused();
    
    await page.keyboard.press('ArrowRight');
    await expect(dashboardPage.targetTab).toBeFocused();
    
    // Test Enter key activation
    await page.keyboard.press('Enter');
    await expect(dashboardPage.targetTab).toHaveAttribute('aria-selected', 'true');
    
    console.log('✅ Keyboard navigation verified');
  });

  test('Screen reader compatibility', async ({ page }) => {
    console.log('🔊 Testing screen reader compatibility');
    
    // Check for ARIA labels on form inputs
    await expect(dashboardPage.monthlyIncomeInput).toHaveAttribute('aria-label');
    await expect(dashboardPage.cantonSelect).toHaveAttribute('aria-label');
    
    // Check for ARIA live regions for dynamic content
    await expect(dashboardPage.fireProjection).toHaveAttribute('aria-live');
    
    // Check for proper button labels
    const buttons = await page.locator('button').all();
    for (const button of buttons) {
      const ariaLabel = await button.getAttribute('aria-label');
      const textContent = await button.textContent();
      
      // Button should have either aria-label or text content
      expect(ariaLabel || textContent).toBeTruthy();
    }
    
    // Check for proper form labels
    const inputs = await page.locator('input').all();
    for (const input of inputs) {
      const id = await input.getAttribute('id');
      if (id) {
        const label = page.locator(`label[for="${id}"]`);
        const ariaLabel = await input.getAttribute('aria-label');
        const ariaLabelledBy = await input.getAttribute('aria-labelledby');
        
        // Input should have label, aria-label, or aria-labelledby
        const hasLabel = await label.count() > 0;
        expect(hasLabel || ariaLabel || ariaLabelledBy).toBeTruthy();
      }
    }
    
    console.log('✅ Screen reader compatibility verified');
  });

  test('Color contrast compliance', async ({ page }) => {
    console.log('🎨 Testing color contrast compliance');
    
    // Input scenario to ensure all elements are visible
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Navigate through different tabs to test various color schemes
    const tabs = [
      dashboardPage.overviewTab,
      dashboardPage.budgetTab,
      dashboardPage.taxOptimizationTab,
      dashboardPage.fireAccelerationTab
    ];
    
    for (const tab of tabs) {
      await tab.click();
      await dashboardPage.waitForCalculations();
      
      // Check that text is visible and readable
      const textElements = await page.locator('p, span, div, h1, h2, h3, h4, h5, h6').all();
      
      for (let i = 0; i < Math.min(textElements.length, 10); i++) {
        const element = textElements[i];
        const textContent = await element.textContent();
        
        if (textContent && textContent.trim().length > 0) {
          // Element should be visible (basic contrast check)
          await expect(element).toBeVisible();
        }
      }
    }
    
    console.log('✅ Color contrast compliance verified');
  });

  test('Focus management', async ({ page }) => {
    console.log('🎯 Testing focus management');
    
    // Test focus trap in modals/dialogs
    const languageSwitcher = page.locator('[data-testid="language-switcher"]');
    if (await languageSwitcher.count() > 0) {
      await languageSwitcher.click();
      
      // Focus should be trapped within the dropdown
      const dropdownOptions = await page.locator('[data-testid*="language-option"]').all();
      if (dropdownOptions.length > 0) {
        await expect(dropdownOptions[0]).toBeFocused();
        
        // Escape should close dropdown and return focus
        await page.keyboard.press('Escape');
        await expect(languageSwitcher).toBeFocused();
      }
    }
    
    // Test focus management during tab navigation
    await dashboardPage.navigateToOverviewTab();
    await dashboardPage.monthlyIncomeInput.focus();
    
    await dashboardPage.navigateToTaxOptimizationTab();
    // Focus should move to appropriate element in new tab
    const focusedElement = await page.locator(':focus').first();
    expect(focusedElement).toBeTruthy();
    
    console.log('✅ Focus management verified');
  });

  test('Alternative text for images and charts', async ({ page }) => {
    console.log('🖼️ Testing alternative text for images and charts');
    
    const scenario = swissTestScenarios.genevaExecutive;
    await dashboardPage.inputScenario(scenario);
    
    // Navigate to tabs with charts
    await dashboardPage.navigateToVizTab();
    
    // Check for SVG charts with proper accessibility
    const svgElements = await page.locator('svg').all();
    for (const svg of svgElements) {
      const ariaLabel = await svg.getAttribute('aria-label');
      const title = await svg.locator('title').first().textContent();
      const desc = await svg.locator('desc').first().textContent();
      
      // SVG should have aria-label, title, or description
      expect(ariaLabel || title || desc).toBeTruthy();
    }
    
    // Check for images with alt text
    const images = await page.locator('img').all();
    for (const img of images) {
      const alt = await img.getAttribute('alt');
      const ariaLabel = await img.getAttribute('aria-label');
      
      // Images should have alt text or aria-label
      expect(alt !== null || ariaLabel !== null).toBeTruthy();
    }
    
    console.log('✅ Alternative text verified');
  });

  test('Error message accessibility', async ({ page }) => {
    console.log('⚠️ Testing error message accessibility');
    
    // Trigger validation errors
    await dashboardPage.enterMonthlyIncome(-1000);
    
    // Check for error message accessibility
    const errorMessage = page.locator('[data-testid="income-error"]');
    if (await errorMessage.count() > 0) {
      // Error should be announced to screen readers
      const ariaLive = await errorMessage.getAttribute('aria-live');
      const role = await errorMessage.getAttribute('role');
      
      expect(ariaLive || role).toBeTruthy();
      
      // Error should be associated with the input
      const inputId = await dashboardPage.monthlyIncomeInput.getAttribute('id');
      const ariaDescribedBy = await dashboardPage.monthlyIncomeInput.getAttribute('aria-describedby');
      
      if (inputId && ariaDescribedBy) {
        expect(ariaDescribedBy).toContain(inputId);
      }
    }
    
    // Test age validation error
    await dashboardPage.enterAge(150);
    
    const ageError = page.locator('[data-testid="age-error"]');
    if (await ageError.count() > 0) {
      await expect(ageError).toBeVisible();
      
      // Should have proper ARIA attributes
      const ariaLive = await ageError.getAttribute('aria-live');
      expect(ariaLive).toBeTruthy();
    }
    
    console.log('✅ Error message accessibility verified');
  });

  test('Mobile accessibility', async ({ browser }) => {
    console.log('📱 Testing mobile accessibility');
    
    // Create mobile context
    const mobileContext = await browser.newContext({
      ...browser.devices()['iPhone 12']
    });
    
    const mobilePage = await mobileContext.newPage();
    const mobileDashboard = new DashboardPage(mobilePage);
    
    await mobileDashboard.goto();
    await mobileDashboard.waitForPageLoad();
    
    // Check touch target sizes (minimum 44px)
    const buttons = await mobilePage.locator('button').all();
    for (const button of buttons) {
      const boundingBox = await button.boundingBox();
      if (boundingBox) {
        expect(boundingBox.width).toBeGreaterThanOrEqual(44);
        expect(boundingBox.height).toBeGreaterThanOrEqual(44);
      }
    }
    
    // Test mobile navigation
    const scenario = swissTestScenarios.bernConservative;
    await mobileDashboard.inputScenario(scenario);
    
    // Verify mobile-specific accessibility features
    await mobileDashboard.checkAccessibility();
    
    await mobileContext.close();
    console.log('✅ Mobile accessibility verified');
  });

  test('High contrast mode support', async ({ page }) => {
    console.log('🔆 Testing high contrast mode support');
    
    // Simulate high contrast mode
    await page.emulateMedia({ colorScheme: 'dark' });
    
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Navigate through tabs to ensure visibility in high contrast
    const tabs = [
      dashboardPage.overviewTab,
      dashboardPage.taxOptimizationTab,
      dashboardPage.swissRelocationTab
    ];
    
    for (const tab of tabs) {
      await tab.click();
      await dashboardPage.waitForCalculations();
      
      // Verify important elements are still visible
      await expect(dashboardPage.fireProjection).toBeVisible();
      await expect(dashboardPage.monthlyTaxAmount).toBeVisible();
    }
    
    console.log('✅ High contrast mode support verified');
  });

  test('Language accessibility', async ({ page }) => {
    console.log('🌍 Testing language accessibility');
    
    // Test language switching accessibility
    const languageSwitcher = page.locator('[data-testid="language-switcher"]');
    if (await languageSwitcher.count() > 0) {
      // Should have proper ARIA attributes
      await expect(languageSwitcher).toHaveAttribute('aria-label');
      
      // Should be keyboard accessible
      await languageSwitcher.focus();
      await page.keyboard.press('Enter');
      
      // Language options should be accessible
      const languageOptions = await page.locator('[data-testid*="language-option"]').all();
      for (const option of languageOptions) {
        await expect(option).toHaveAttribute('role');
      }
      
      // Close dropdown
      await page.keyboard.press('Escape');
    }
    
    // Test content in different languages
    const scenario = swissTestScenarios.vaudFamily;
    await dashboardPage.inputScenario(scenario);
    
    // Switch to German and verify accessibility is maintained
    if (await languageSwitcher.count() > 0) {
      await languageSwitcher.click();
      await page.locator('[data-testid="language-option-de"]').click();
      await page.waitForTimeout(1000);
      
      // Check accessibility in German
      await dashboardPage.checkAccessibility();
    }
    
    console.log('✅ Language accessibility verified');
  });
});
