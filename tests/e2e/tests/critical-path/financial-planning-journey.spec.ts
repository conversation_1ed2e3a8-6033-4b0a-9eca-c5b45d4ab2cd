import { expect, test } from '@playwright/test';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';
import { DashboardPage } from '../../pages/dashboard-page';

test.describe('Financial Planning Journey - Critical Path', () => {
  let dashboardPage: DashboardPage;

  // Configure tests to run sequentially and with longer timeout
  test.describe.configure({ mode: 'serial', timeout: 60000 });

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test('Complete FIRE planning workflow for Zurich Professional', async ({ page }) => {
    const scenario = swissTestScenarios.zurichProfessional;

    // Step 1: Input financial data
    await test.step('Input personal and financial information', async () => {
      await dashboardPage.inputScenario(scenario);
    });

    // Step 2: Verify calculations
    await test.step('Verify FIRE calculations', async () => {
      await dashboardPage.expectFIREProjection(scenario.expectedResults.fireAge);
      await dashboardPage.expectMonthlyTax(scenario.expectedResults.monthlyTax);
      await dashboardPage.expectSavingsRate(scenario.expectedResults.savingsRate);
    });

    // Step 3: Test data persistence
    await test.step('Verify data persistence after page reload', async () => {
      await dashboardPage.verifyDataPersistence(scenario);
    });

    // Step 4: Navigate through all tabs
    await test.step('Navigate through all application tabs', async () => {
      await dashboardPage.navigateToBudgetTab();
      await dashboardPage.expectVisible('[data-testid="budget-content"]');

      await dashboardPage.navigateToTargetTab();
      await dashboardPage.expectVisible('[data-testid="target-content"]');

      await dashboardPage.navigateToAnalysisTab();
      await dashboardPage.expectVisible('[data-testid="analysis-content"]');

      await dashboardPage.navigateToReportsTab();
      await dashboardPage.expectVisible('[data-testid="reports-content"]');
    });

    // Step 5: Test export functionality
    await test.step('Export financial data', async () => {
      await dashboardPage.exportData();
    });
  });

  test('Complete workflow for Vaud Family scenario', async ({ page }) => {
    const scenario = swissTestScenarios.vaudFamily;

    await test.step('Input family financial scenario', async () => {
      await dashboardPage.inputScenario(scenario);
    });

    await test.step('Verify family-specific calculations', async () => {
      // Family scenarios should have different tax implications
      await dashboardPage.expectFIREProjection(scenario.expectedResults.fireAge);
      await dashboardPage.expectMonthlyTax(scenario.expectedResults.monthlyTax);

      // Verify that married status affects calculations
      const savingsRate = await dashboardPage.getSavingsRate();
      expect(savingsRate).toBeGreaterThan(0);
      expect(savingsRate).toBeLessThan(100);
    });

    await test.step('Test scenario switching', async () => {
      // Switch to single status and verify calculations change
      await dashboardPage.selectCivilStatus('single');
      await dashboardPage.waitForCalculations();

      const newTaxAmount = await dashboardPage.getMonthlyTaxAmount();
      expect(newTaxAmount).not.toBe(scenario.expectedResults.monthlyTax);
    });
  });

  test('High-income Geneva Executive scenario', async ({ page }) => {
    const scenario = swissTestScenarios.genevaExecutive;

    await test.step('Input high-income scenario', async () => {
      await dashboardPage.inputScenario(scenario);
    });

    await test.step('Verify high-income tax calculations', async () => {
      // High income should result in higher tax rates
      const monthlyTax = await dashboardPage.getMonthlyTaxAmount();
      expect(monthlyTax).toBeGreaterThan(3000); // High earners pay significant taxes

      await dashboardPage.expectFIREProjection(scenario.expectedResults.fireAge);
    });

    await test.step('Test wealth tax implications', async () => {
      // Geneva has wealth tax - verify it's calculated
      await dashboardPage.navigateToAnalysisTab();
      await dashboardPage.expectVisible('[data-testid="wealth-tax-analysis"]');
    });
  });

  test('Conservative Bern saver scenario', async ({ page }) => {
    const scenario = swissTestScenarios.bernConservative;

    await test.step('Input conservative scenario', async () => {
      await dashboardPage.inputScenario(scenario);
    });

    await test.step('Verify conservative savings approach', async () => {
      // Conservative approach should show later FIRE age but higher savings rate
      await dashboardPage.expectFIREProjection(scenario.expectedResults.fireAge);

      const savingsRate = await dashboardPage.getSavingsRate();
      expect(savingsRate).toBeGreaterThan(35); // High savings rate
    });

    await test.step('Test Pillar 3a optimization', async () => {
      await dashboardPage.navigateToAnalysisTab();

      // Should show Pillar 3a recommendations
      await dashboardPage.expectVisible('[data-testid="pillar3a-optimization"]');
      await dashboardPage.expectText('[data-testid="pillar3a-recommendation"]', 'CHF 7\'056');
    });
  });

  test('Error handling and edge cases', async ({ page }) => {
    await test.step('Test invalid input handling', async () => {
      // Test negative income
      await dashboardPage.enterMonthlyIncome(-1000);
      await dashboardPage.expectVisible('[data-testid="income-error"]');

      // Test unrealistic age
      await dashboardPage.enterAge(150);
      await dashboardPage.expectVisible('[data-testid="age-error"]');

      // Test excessive Pillar 3a contribution
      await dashboardPage.enterPillar3aContribution(50000);
      await dashboardPage.expectVisible('[data-testid="pillar3a-error"]');
    });

    await test.step('Test recovery from errors', async () => {
      // Input valid data and verify errors clear
      const scenario = swissTestScenarios.zurichProfessional;
      await dashboardPage.inputScenario(scenario);

      await dashboardPage.expectHidden('[data-testid="income-error"]');
      await dashboardPage.expectHidden('[data-testid="age-error"]');
      await dashboardPage.expectHidden('[data-testid="pillar3a-error"]');
    });
  });

  test('Performance benchmarks', async ({ page }) => {
    await test.step('Measure page load performance', async () => {
      const loadTime = await dashboardPage.measurePageLoadTime();
      expect(loadTime).toBeLessThan(3000); // Should load in under 3 seconds
    });

    await test.step('Measure calculation performance', async () => {
      const startTime = Date.now();
      await dashboardPage.enterMonthlyIncome(10000);
      await dashboardPage.waitForCalculations();
      const calculationTime = Date.now() - startTime;

      expect(calculationTime).toBeLessThan(1000); // Calculations should complete in under 1 second
    });
  });

  test('Accessibility compliance', async ({ page }) => {
    await test.step('Check basic accessibility', async () => {
      await dashboardPage.checkAccessibility();
    });

    await test.step('Test keyboard navigation', async () => {
      // Test tab navigation through form fields
      await page.keyboard.press('Tab');
      await expect(dashboardPage.monthlyIncomeInput).toBeFocused();

      await page.keyboard.press('Tab');
      await expect(dashboardPage.bonusIncomeInput).toBeFocused();

      await page.keyboard.press('Tab');
      await expect(dashboardPage.pillar3aInput).toBeFocused();
    });

    await test.step('Test screen reader compatibility', async () => {
      // Verify ARIA labels are present
      await expect(dashboardPage.monthlyIncomeInput).toHaveAttribute('aria-label');
      await expect(dashboardPage.cantonSelect).toHaveAttribute('aria-label');
      await expect(dashboardPage.fireProjection).toHaveAttribute('aria-live');
    });
  });
});
