import React from "react";
import { useTranslation } from "react-i18next";

interface SmartDashboardProps {
  userProgress: UserProgress;
  financialData: FinancialData;
  darkMode: boolean;
}

interface UserProgress {
  hasBasicInfo: boolean;
  hasIncomeData: boolean;
  hasExpenseData: boolean;
  hasSavingsGoals: boolean;
  hasSwissConfig: boolean;
  completionPercentage: number;
}

interface FinancialData {
  totalMonthlyIncome: number;
  totalExpenses: number;
  savingsRate: number;
  fireProgress: number;
  monthsToFire: number;
  currentBalance: number;
}

const SmartDashboard: React.FC<SmartDashboardProps> = ({
  userProgress,
  financialData,
  darkMode,
}) => {
  const { t } = useTranslation();

  const getNextAction = () => {
    if (!userProgress.hasBasicInfo) {
      return {
        title: "Complete Your Profile",
        description: "Add your age, canton, and employment status",
        action: "Get Started",
        icon: "👤",
        priority: "high",
      };
    }
    if (!userProgress.hasIncomeData) {
      return {
        title: "Add Your Income",
        description: "Enter your salary and other income sources",
        action: "Add Income",
        icon: "💰",
        priority: "high",
      };
    }
    if (!userProgress.hasExpenseData) {
      return {
        title: "Track Your Expenses",
        description: "Add your monthly expenses to see your savings potential",
        action: "Add Expenses",
        icon: "📊",
        priority: "medium",
      };
    }
    if (financialData.savingsRate < 10) {
      return {
        title: "Optimize Your Savings",
        description:
          "Your savings rate is low. Let's find opportunities to save more",
        action: "Optimize",
        icon: "📈",
        priority: "medium",
      };
    }
    return {
      title: "Explore Tax Optimization",
      description: "Maximize your Pillar 3a and explore cantonal tax benefits",
      action: "Optimize Taxes",
      icon: "🇨🇭",
      priority: "low",
    };
  };

  const nextAction = getNextAction();

  return (
    <div className="smart-dashboard space-y-6">
      {/* Progress Section */}
      <div
        className={`p-6 rounded-xl ${
          darkMode
            ? "bg-gradient-to-r from-blue-900/50 to-purple-900/50 border border-blue-500/25"
            : "bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200"
        }`}
      >
        <div className="flex items-center justify-between mb-4">
          <h2
            className={`text-2xl font-bold ${
              darkMode ? "text-white" : "text-gray-800"
            }`}
          >
            🚀 Your FIRE Journey
          </h2>
          <span
            className={`text-sm px-3 py-1 rounded-full ${
              darkMode ? "bg-blue-600 text-white" : "bg-blue-100 text-blue-800"
            }`}
          >
            {userProgress.completionPercentage}% Complete
          </span>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div
            className={`w-full h-3 rounded-full ${
              darkMode ? "bg-gray-700" : "bg-gray-200"
            }`}
          >
            <div
              className="h-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500"
              style={{ width: `${userProgress.completionPercentage}%` }}
            />
          </div>
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <MetricCard
            icon="💰"
            label="Monthly Income"
            value={`CHF ${(
              financialData.totalMonthlyIncome || 0
            ).toLocaleString()}`}
            darkMode={darkMode}
          />
          <MetricCard
            icon="📈"
            label="Savings Rate"
            value={`${(financialData.savingsRate || 0).toFixed(1)}%`}
            darkMode={darkMode}
          />
          <MetricCard
            icon="🎯"
            label="FIRE Progress"
            value={`${(financialData.fireProgress || 0).toFixed(0)}%`}
            darkMode={darkMode}
          />
          <MetricCard
            icon="📅"
            label="Months to FIRE"
            value={
              (financialData.monthsToFire || 0) > 0
                ? `${financialData.monthsToFire || 0}`
                : "∞"
            }
            darkMode={darkMode}
          />
        </div>
      </div>

      {/* Next Action Card */}
      <div
        className={`p-6 rounded-xl ${
          nextAction.priority === "high"
            ? darkMode
              ? "bg-red-900/30 border border-red-500/50"
              : "bg-red-50 border border-red-200"
            : nextAction.priority === "medium"
            ? darkMode
              ? "bg-yellow-900/30 border border-yellow-500/50"
              : "bg-yellow-50 border border-yellow-200"
            : darkMode
            ? "bg-green-900/30 border border-green-500/50"
            : "bg-green-50 border border-green-200"
        }`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-3xl">{nextAction.icon}</span>
            <div>
              <h3
                className={`text-lg font-semibold ${
                  darkMode ? "text-white" : "text-gray-800"
                }`}
              >
                {nextAction.title}
              </h3>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                }`}
              >
                {nextAction.description}
              </p>
            </div>
          </div>
          <button
            className={`px-4 py-2 rounded-lg font-medium transition-all hover:scale-105 ${
              nextAction.priority === "high"
                ? "bg-red-500 hover:bg-red-600 text-white"
                : nextAction.priority === "medium"
                ? "bg-yellow-500 hover:bg-yellow-600 text-white"
                : "bg-green-500 hover:bg-green-600 text-white"
            }`}
          >
            {nextAction.action}
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div
        className={`p-6 rounded-xl ${
          darkMode
            ? "bg-gray-800/70 border border-gray-600/50"
            : "bg-white border border-gray-200"
        }`}
      >
        <h3
          className={`text-lg font-semibold mb-4 ${
            darkMode ? "text-white" : "text-gray-800"
          }`}
        >
          ⚡ Quick Actions
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <QuickActionButton icon="💰" label="Add Income" darkMode={darkMode} />
          <QuickActionButton
            icon="📊"
            label="Add Expense"
            darkMode={darkMode}
          />
          <QuickActionButton icon="🎯" label="Set Goal" darkMode={darkMode} />
          <QuickActionButton
            icon="📈"
            label="View Report"
            darkMode={darkMode}
          />
        </div>
      </div>

      {/* Swiss Insights */}
      <div
        className={`p-6 rounded-xl ${
          darkMode
            ? "bg-gray-800/70 border border-red-500/25"
            : "bg-white border border-red-200"
        }`}
      >
        <h3
          className={`text-lg font-semibold mb-4 ${
            darkMode ? "text-white" : "text-gray-800"
          } flex items-center`}
        >
          🇨🇭 Swiss Financial Insights
        </h3>
        <div className="space-y-3">
          <InsightCard
            title="Pillar 3a Optimization"
            description="You can save CHF 1,200 in taxes by maximizing your Pillar 3a contributions"
            action="Optimize Now"
            darkMode={darkMode}
          />
          <InsightCard
            title="Canton Tax Comparison"
            description="Moving to Zug could save you CHF 3,500 annually in taxes"
            action="Compare Cantons"
            darkMode={darkMode}
          />
        </div>
      </div>
    </div>
  );
};

const MetricCard: React.FC<{
  icon: string;
  label: string;
  value: string;
  darkMode: boolean;
}> = ({ icon, label, value, darkMode }) => (
  <div
    className={`p-3 rounded-lg ${darkMode ? "bg-gray-700/50" : "bg-white/80"}`}
  >
    <div className="flex items-center space-x-2 mb-1">
      <span className="text-lg">{icon}</span>
      <span
        className={`text-xs font-medium ${
          darkMode ? "text-gray-300" : "text-gray-600"
        }`}
      >
        {label}
      </span>
    </div>
    <div
      className={`text-lg font-bold ${
        darkMode ? "text-white" : "text-gray-800"
      }`}
    >
      {value}
    </div>
  </div>
);

const QuickActionButton: React.FC<{
  icon: string;
  label: string;
  darkMode: boolean;
}> = ({ icon, label, darkMode }) => (
  <button
    className={`p-3 rounded-lg transition-all hover:scale-105 ${
      darkMode
        ? "bg-gray-700 hover:bg-gray-600 text-white"
        : "bg-gray-100 hover:bg-gray-200 text-gray-800"
    }`}
  >
    <div className="text-xl mb-1">{icon}</div>
    <div className="text-xs font-medium">{label}</div>
  </button>
);

const InsightCard: React.FC<{
  title: string;
  description: string;
  action: string;
  darkMode: boolean;
}> = ({ title, description, action, darkMode }) => (
  <div
    className={`p-4 rounded-lg border ${
      darkMode ? "bg-gray-700/30 border-gray-600" : "bg-gray-50 border-gray-200"
    }`}
  >
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <h4
          className={`font-semibold ${
            darkMode ? "text-white" : "text-gray-800"
          }`}
        >
          {title}
        </h4>
        <p
          className={`text-sm ${darkMode ? "text-gray-300" : "text-gray-600"}`}
        >
          {description}
        </p>
      </div>
      <button
        className={`ml-4 px-3 py-1 rounded text-sm font-medium ${
          darkMode
            ? "bg-blue-600 hover:bg-blue-700 text-white"
            : "bg-blue-500 hover:bg-blue-600 text-white"
        }`}
      >
        {action}
      </button>
    </div>
  </div>
);

export default SmartDashboard;
