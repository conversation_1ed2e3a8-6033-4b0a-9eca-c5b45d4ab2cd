{"labels": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "email": "E-Mail", "phone": "Telefon", "address": "<PERSON><PERSON><PERSON>", "city": "Stadt", "postalCode": "<PERSON><PERSON><PERSON><PERSON>", "canton": "<PERSON><PERSON>", "country": "Land", "birthDate": "Geburtsdatum", "age": "Alter", "currentAge": "Aktuelles Alter", "retirementAge": "Pensionsalter"}, "placeholders": {"enterAmount": "<PERSON><PERSON> e<PERSON>ben", "selectOption": "Option auswählen", "enterText": "Text eingeben", "selectDate": "Da<PERSON> ausw<PERSON>en", "enterEmail": "E-Mail-Ad<PERSON><PERSON>", "enterPhone": "Telefonnummer eingeben"}, "income": {"title": "Einkommensinformationen", "monthlyIncome": "Mona<PERSON>einkommen", "workTime": "Arbeitszeit: {{percentage}}%", "companyIncome": "Firmeneinkommen", "startYear": "<PERSON><PERSON>r", "growthRate": "Jährliche Wachstumsrate", "contractIncome": "Auftragseinkommen", "investmentIncome": "Kapitalerträge", "otherIncome": "Sonstiges Einkommen"}, "expenses": {"title": "Ausgabeninformationen", "monthlyExpenses": "Monatsausgaben", "housing": "Wohnen & Nebenkosten", "food": "Lebensmittel & Einkäufe", "transportation": "Transport", "insurance": "Versicherungen", "healthcare": "Gesundheit", "entertainment": "Unterhaltung", "travel": "<PERSON><PERSON><PERSON>", "shopping": "Einkäufe", "other": "Sonstige Ausgaben", "addCategory": "<PERSON><PERSON><PERSON>", "categoryName": "<PERSON><PERSON><PERSON><PERSON>"}, "savings": {"title": "Sparinformationen", "currentSavings": "Aktuelle Gesamtersparnisse", "portfolioStart": "Portfolio-Start", "monthlySavings": "Monatliche Ersparnisse", "emergencyFund": "Notfallfonds", "investmentGoals": "<PERSON><PERSON><PERSON><PERSON>", "riskTolerance": "Risikotoleranz", "timeHorizon": "Zeithorizont"}, "swiss": {"title": "Schweizer Konfiguration", "canton": "<PERSON><PERSON><PERSON><PERSON>", "civilStatus": "Zivilstand", "hasSecondPillar": "Zweite Säule (BVG)", "currentPillar3a": "Aktuelle Säule 3a", "maxPillar3a": "Max. Säule 3a Beitrag", "healthInsurance": "Krankenversicherungsprämie", "franchise": "Krankenversicherungs-Franchise"}, "validation": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalidEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "invalidPhone": "Bitte geben Si<PERSON> eine gültige Telefonnummer ein", "invalidAmount": "Bitte geben Si<PERSON> einen gültigen Betrag ein", "minAmount": "Betrag muss mindestens {{min}} sein", "maxAmount": "Betrag darf {{max}} nicht überschreiten", "invalidPercentage": "Bitte geben Sie einen Prozentsatz zwischen 0 und 100 ein", "invalidAge": "Bitte geben Si<PERSON> ein gültiges Alter ein", "futureDate": "Datum darf nicht in der Zukunft liegen", "pastDate": "Datum darf nicht in der Vergangenheit liegen"}, "help": {"monthlyIncome": "Geben Sie Ihr monatliches Bruttoeinkommen aus der Anstellung ein", "workTime": "Prozentsatz der Vollzeitarbeit (100% = Vollzeit)", "companyIncome": "Zusätzliches Einkommen aus Ihrem eigenen Unternehmen", "pillar3a": "Steuerlich begünstigte Altersvorsorge (max. CHF 7'056 in 2024)", "emergencyFund": "Empfohlen: 3-6 Monate Ausgaben", "riskTolerance": "Ihr Komfort mit Anlage-Volatilität", "canton": "<PERSON>hr Wohnkanton beeinflusst die Steuerberechnungen"}, "tooltips": {"info": "Weitere Informationen", "help": "<PERSON><PERSON><PERSON>", "example": "Beispiel", "calculation": "Wie dies berechnet wird", "recommendation": "Unsere Empfehlung"}}