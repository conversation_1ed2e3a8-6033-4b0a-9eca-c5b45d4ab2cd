{"validation": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalidEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "invalidPhone": "Bitte geben Si<PERSON> eine gültige Telefonnummer ein", "invalidAmount": "Bitte geben Si<PERSON> einen gültigen Betrag ein", "invalidPercentage": "Bitte geben Sie einen Prozentsatz zwischen 0 und 100 ein", "minAmount": "Betrag muss mindestens {{min}} sein", "maxAmount": "Betrag darf {{max}} nicht überschreiten", "minValue": "Wert muss mindestens {{min}} sein", "maxValue": "Wert darf {{max}} nicht überschreiten", "invalidAge": "Bitte geben Sie ein gültiges Alter zwischen 18 und 100 ein", "invalidDate": "<PERSON>te geben Si<PERSON> ein gültiges Datum ein", "futureDate": "Datum darf nicht in der Zukunft liegen", "pastDate": "Datum darf nicht in der Vergangenheit liegen", "invalidFormat": "Ungültiges Format"}, "calculation": {"insufficientData": "Unzureichende Daten für Berechnung", "invalidInput": "Ungültige Eingabeparameter", "calculationError": "Fehler bei der Berechnung", "negativeResult": "Berechnung ergab negativen Wert", "divisionByZero": "Division durch <PERSON><PERSON>", "overflow": "Zahl zu gross für Berechnung"}, "data": {"loadError": "Fehler beim Laden der Daten", "saveError": "Fehler beim Speichern der Daten", "corruptedData": "Daten scheinen beschädigt zu sein", "missingData": "Erforderliche Daten fehlen", "invalidData": "Datenformat ist ungültig", "outdatedData": "Daten könnten veraltet sein"}, "network": {"connectionError": "Netzwerkverbindungsfehler", "timeout": "Anfrage-Timeout", "serverError": "Serverfehler aufgetreten", "notFound": "Ressource nicht gefunden", "unauthorized": "Unbefugter Zugriff", "forbidden": "Zugriff verboten"}, "application": {"unexpectedError": "Ein unerwarteter Fehler ist aufgetreten", "featureUnavailable": "Diese Funktion ist derzeit nicht verfügbar", "browserNotSupported": "<PERSON><PERSON> Browser wird nicht unterstützt", "javascriptDisabled": "JavaScript muss aktiviert sein", "cookiesDisabled": "Cookies müssen aktiviert sein", "localStorageUnavailable": "Lokaler Speicher ist nicht verfügbar"}, "swiss": {"invalidCanton": "Bitte wählen Sie einen gültigen Schweizer Kanton", "pillar3aExceeded": "Säule 3a Beitrag überschreitet Jahresgrenze", "invalidTaxYear": "Bitte wählen Sie ein gültiges Steuerjahr", "missingTaxData": "Steuerdaten für gewählten Kanton nicht verfügbar", "invalidCivilStatus": "Bitte wählen Sie einen gültigen Zivilstand"}, "messages": {"tryAgain": "Bitte versuchen Sie es erneut", "contactSupport": "Falls das Problem weiterhin besteht, kontaktieren Sie den Support", "checkConnection": "Bitte überprüfen Sie Ihre Internetverbindung", "refreshPage": "Versuchen Sie, die Seite zu aktualisieren", "clearCache": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON>, <PERSON><PERSON>-<PERSON><PERSON> zu leeren", "updateBrowser": "Bitte aktualisieren Sie Ihren Browser"}, "actions": {"retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "close": "<PERSON><PERSON><PERSON><PERSON>", "reload": "Neu laden", "goBack": "Zurück gehen", "contactSupport": "Support kontaktieren"}}